import React, { useState, useCallback, useRef, useEffect } from "react";
import { MapPin, Plus, Save, Trash2 } from "lucide-react";
import ComponentCard from "../../components/common/ComponentCard";
import Label from "../../components/form/Label";
import Input from "../../components/form/input/InputField";
import { BranchData } from "../../service/type/Branch";

interface BranchLocation {
  id: string;
  name: string;
  address: string;
  longitude: number;
  latitude: number;
  radius: number;
}

declare global {
  interface Window {
    L: any; // Leaflet global
  }
}
const Branchadd = () => {
  const [branches, setBranches] = useState<BranchLocation[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<BranchLocation | null>(null);
  const [searchLocation, setSearchLocation] = useState('cambodia');
  const [isEditing, setIsEditing] = useState(false);
  const [map, setMap] = useState<any>(null);
  const [markers, setMarkers] = useState<any[]>([]);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    longitude: '',
    latitude: '',
    radius: '50'
  });

  const mapRef = useRef<HTMLDivElement>(null);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) return;

    // Load Leaflet CSS and JS
    const loadLeaflet = async () => {
      // Add CSS
      if (!document.querySelector('link[href*="leaflet"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css';
        document.head.appendChild(link);
      }

      // Load JS
      if (!window.L) {
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.js';
        document.head.appendChild(script);
        
        script.onload = () => {
          initializeMap();
        };
      } else {
        initializeMap();
      }
    };

    const initializeMap = () => {
      if (!window.L || map) return;

      // Initialize map centered on Phnom Penh, Cambodia
      const leafletMap = window.L.map(mapRef.current, {
        center: [11.5564, 104.9282],
        zoom: 13,
        zoomControl: true,
      });

      // Add tile layer
      window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(leafletMap);

      // Add click handler
      leafletMap.on('click', (e: any) => {
        const { lat, lng } = e.latlng;
        setFormData(prev => ({
          ...prev,
          longitude: lng.toFixed(10),
          latitude: lat.toFixed(10)
        }));
        setIsEditing(true);
      });

      setMap(leafletMap);
    };

    loadLeaflet();

    return () => {
      if (map) {
        map.remove();
      }
    };
  }, []);

  // Update markers when branches change
  useEffect(() => {
    if (!map || !window.L) return;

    // Clear existing markers
    markers.forEach(marker => map.removeLayer(marker));
    setMarkers([]);

    // Add new markers
    const newMarkers: any[] = [];
    
    branches.forEach(branch => {
      // Create marker
      const marker = window.L.marker([branch.latitude, branch.longitude])
        .addTo(map)
        .bindPopup(`
          <div style="text-align: center;">
            <strong>${branch.name}</strong><br/>
            ${branch.address}<br/>
            <small>Lat: ${branch.latitude.toFixed(6)}, Lng: ${branch.longitude.toFixed(6)}</small>
          </div>
        `);

      // Create radius circle
      const circle = window.L.circle([branch.latitude, branch.longitude], {
        color: '#3b82f6',
        fillColor: '#3b82f6',
        fillOpacity: 0.1,
        radius: branch.radius * 10 // Convert to meters (approximate)
      }).addTo(map);

      // Add longitude and latitude lines
      const latLine = window.L.polyline([
        [branch.latitude, branch.longitude - 0.01],
        [branch.latitude, branch.longitude + 0.01]
      ], {
        color: '#4a5568',
        weight: 2,
        dashArray: '5, 5'
      }).addTo(map);

      const lngLine = window.L.polyline([
        [branch.latitude - 0.01, branch.longitude],
        [branch.latitude + 0.01, branch.longitude]
      ], {
        color: '#4a5568',
        weight: 2,
        dashArray: '5, 5'
      }).addTo(map);

      // Add radius label
      const radiusEndPoint = [
        branch.latitude + (branch.radius * 10 * 0.00001),
        branch.longitude
      ];
      
      const radiusLine = window.L.polyline([
        [branch.latitude, branch.longitude],
        radiusEndPoint
      ], {
        color: '#3b82f6',
        weight: 2
      }).addTo(map);

      // Add labels using divIcon
      const radiusLabel = window.L.marker(radiusEndPoint, {
        icon: window.L.divIcon({
          className: 'radius-label',
          html: `<div style="background: white; padding: 2px 4px; border-radius: 3px; font-size: 12px; font-weight: bold; color: #374151; border: 1px solid #d1d5db;">radius</div>`,
          iconSize: [50, 20],
          iconAnchor: [25, 10]
        })
      }).addTo(map);

      const coordLabel = window.L.marker([branch.latitude - 0.008, branch.longitude + 0.008], {
        icon: window.L.divIcon({
          className: 'coord-label',
          html: `<div style="background: white; padding: 2px 4px; border-radius: 3px; font-size: 12px; font-weight: bold; color: #374151; border: 1px solid #d1d5db;">longitude and latitude</div>`,
          iconSize: [140, 20],
          iconAnchor: [70, 10]
        })
      }).addTo(map);

      // Add click handler to marker
      marker.on('click', () => {
        handleEditBranch(branch);
      });

      newMarkers.push(marker, circle, latLine, lngLine, radiusLine, radiusLabel, coordLabel);
    });

    setMarkers(newMarkers);
  }, [branches, map]);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveBranch = () => {
    if (!formData.name || !formData.address || !formData.longitude || !formData.latitude) {
      alert('Please fill in all required fields');
      return;
    }

    const newBranch: BranchLocation = {
      id: selectedBranch?.id || Date.now().toString(),
      name: formData.name,
      address: formData.address,
      longitude: parseFloat(formData.longitude),
      latitude: parseFloat(formData.latitude),
      radius: parseInt(formData.radius)
    };

    if (selectedBranch) {
      setBranches(prev => prev.map(branch => 
        branch.id === selectedBranch.id ? newBranch : branch
      ));
    } else {
      setBranches(prev => [...prev, newBranch]);
    }

    handleClearForm();
  };

  const handleEditBranch = (branch: BranchLocation) => {
    setSelectedBranch(branch);
    setFormData({
      name: branch.name,
      address: branch.address,
      longitude: branch.longitude.toString(),
      latitude: branch.latitude.toString(),
      radius: branch.radius.toString()
    });
    setIsEditing(true);

    // Center map on selected branch
    if (map) {
      map.setView([branch.latitude, branch.longitude], 15);
    }
  };

  const handleDeleteBranch = (branchId: string) => {
    setBranches(prev => prev.filter(branch => branch.id !== branchId));
    if (selectedBranch?.id === branchId) {
      handleClearForm();
    }
  };

  const handleClearForm = () => {
    setSelectedBranch(null);
    setFormData({
      name: '',
      address: '',
      longitude: '',
      latitude: '',
      radius: '50'
    });
    setIsEditing(false);
  };

  const handleSearchLocation = async () => {
    if (!searchLocation.trim() || !map) return;

    try {
      // Simple geocoding using Nominatim (OpenStreetMap)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchLocation)}&limit=1`
      );
      const data = await response.json();
      
      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        map.setView([parseFloat(lat), parseFloat(lon)], 13);
      } else {
        alert('Location not found');
      }
    } catch (error) {
      alert('Error searching for location');
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white">
      <h1 className="text-2xl font-bold mb-6 text-gray-800">Branch Location Manager</h1>
      
      {/* Search Location */}
      <div className="mb-4 flex gap-2">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Search Branch Location
          </label>
          <input
            type="text"
            value={searchLocation}
            onChange={(e) => setSearchLocation(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearchLocation()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter location to search..."
          />
        </div>
        <div className="flex items-end">
          <button
            onClick={handleSearchLocation}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Search
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Map Section */}
        <div className="space-y-4">
          <div className="relative">
            <div 
              ref={mapRef}
              className="w-full h-96 border-2 border-gray-300 rounded-lg overflow-hidden"
              style={{ minHeight: '400px' }}
            />
            <div className="absolute bottom-2 left-2 bg-white bg-opacity-90 px-2 py-1 rounded text-xs z-[1000]">
              Click on map to place branch
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-800">
            {selectedBranch ? 'Edit Branch' : 'Add New Branch'}
          </h2>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Branch Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter branch name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Branch Address *
            </label>
            <input
              type="text"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter branch address"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Longitude *
              </label>
              <input
                type="text"
                value={formData.longitude}
                onChange={(e) => handleInputChange('longitude', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.0000000000"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Latitude *
              </label>
              <input
                type="text"
                value={formData.latitude}
                onChange={(e) => handleInputChange('latitude', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.0000000000"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Radius (meters)
            </label>
            <input
              type="number"
              value={formData.radius}
              onChange={(e) => handleInputChange('radius', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="50"
              min="10"
              max="5000"
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleSaveBranch}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              {selectedBranch ? 'Update' : 'Save'} Branch
            </button>
            {isEditing && (
              <button
                onClick={handleClearForm}
                className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Branch List */}
      {branches.length > 0 && (
        <div className="mt-8">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Saved Branches ({branches.length})</h2>
          <div className="grid gap-4">
            {branches.map((branch) => (
              <div key={branch.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-red-500" />
                      {branch.name}
                    </h3>
                    <p className="text-sm text-gray-600 ml-6">{branch.address}</p>
                    <p className="text-xs text-gray-500 mt-1 ml-6">
                      Coordinates: {branch.latitude.toFixed(6)}, {branch.longitude.toFixed(6)} | 
                      Radius: {branch.radius}m
                    </p>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <button
                      onClick={() => handleEditBranch(branch)}
                      className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteBranch(branch.id)}
                      className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors flex items-center gap-1"
                    >
                      <Trash2 className="w-3 h-3" />
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Branchadd;
